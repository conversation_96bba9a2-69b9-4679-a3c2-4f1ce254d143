package com.nttdata.ndvn.user.infrastructure.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Database configuration for User Management SCS.
 * 
 * This configuration class enables JPA repositories and transaction management
 * for the user management domain.
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.nttdata.ndvn.user.infrastructure.repository")
@EntityScan(basePackages = "com.nttdata.ndvn.user.domain.model")
@EnableTransactionManagement
public class DatabaseConfig {
}
